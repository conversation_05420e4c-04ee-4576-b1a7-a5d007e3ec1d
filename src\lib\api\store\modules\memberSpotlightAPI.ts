/**
 * Member Spotlight API Module
 * Handles all member spotlight related operations
 */

import { supabase } from '@/lib/supabase';
import type { 
  MemberSpotlight, 
  MemberSpotlightFormData, 
  StoreUser 
} from '../types/communityShowcaseTypes';
import { 
  TABLE_NAMES, 
  QUERY_SELECTORS, 
  QUERY_LIMITS, 
  ERROR_MESSAGES 
} from '../constants/communityShowcaseConstants';
import { 
  getCurrentISOString,
  deduplicateStoreUsers,
  buildMemberSearchCondition,
  buildSpotlightDateFilter,
  validateStoreId,
  logErrorWithContext
} from '../utils/communityShowcaseUtils';

/**
 * Member Spotlight API operations
 */
export class MemberSpotlightAPI {
  /**
   * Get active member spotlights with user data
   */
  static async getActiveSpotlights(storeId: string): Promise<MemberSpotlight[]> {
    if (!validateStoreId(storeId)) {
      throw new Error('Invalid store ID');
    }

    const now = getCurrentISOString();

    const { data, error } = await supabase
      .from(TABLE_NAMES.STORE_COMMUNITY_SHOWCASE)
      .select(QUERY_SELECTORS.MEMBER_SPOTLIGHT_WITH_USER)
      .eq('store_id', storeId)
      .eq('show_member_spotlights', true)
      .not('featured_member_id', 'is', null)
      .or(buildSpotlightDateFilter(now))
      .order('spotlight_start_date', { ascending: false })
      .limit(QUERY_LIMITS.DEFAULT_SPOTLIGHTS);

    if (error) {
      logErrorWithContext('getActiveSpotlights', error);
      throw new Error(ERROR_MESSAGES.FETCH_SPOTLIGHTS);
    }

    return data || [];
  }

  /**
   * Create a new member spotlight
   */
  static async createMemberSpotlight(storeId: string, spotlightData: MemberSpotlightFormData): Promise<MemberSpotlight> {
    if (!validateStoreId(storeId)) {
      throw new Error('Invalid store ID');
    }

    const { data, error } = await supabase
      .from(TABLE_NAMES.STORE_COMMUNITY_SHOWCASE)
      .insert({
        store_id: storeId,
        ...spotlightData,
        show_member_spotlights: true,
      })
      .select(QUERY_SELECTORS.MEMBER_SPOTLIGHT_WITH_USER)
      .single();

    if (error) {
      logErrorWithContext('createMemberSpotlight', error);
      throw new Error(ERROR_MESSAGES.CREATE_SPOTLIGHT);
    }

    return data;
  }

  /**
   * Update member spotlight
   */
  static async updateMemberSpotlight(
    storeId: string, 
    spotlightId: string, 
    updates: Partial<MemberSpotlightFormData>
  ): Promise<void> {
    if (!validateStoreId(storeId)) {
      throw new Error('Invalid store ID');
    }

    const { error } = await supabase
      .from(TABLE_NAMES.STORE_COMMUNITY_SHOWCASE)
      .update({
        ...updates,
        updated_at: getCurrentISOString(),
      })
      .eq('id', spotlightId)
      .eq('store_id', storeId);

    if (error) {
      logErrorWithContext('updateMemberSpotlight', error);
      throw new Error(ERROR_MESSAGES.UPDATE_SPOTLIGHT);
    }
  }

  /**
   * Delete member spotlight
   */
  static async deleteMemberSpotlight(storeId: string, spotlightId: string): Promise<void> {
    if (!validateStoreId(storeId)) {
      throw new Error('Invalid store ID');
    }

    const { error } = await supabase
      .from(TABLE_NAMES.STORE_COMMUNITY_SHOWCASE)
      .delete()
      .eq('id', spotlightId)
      .eq('store_id', storeId);

    if (error) {
      logErrorWithContext('deleteMemberSpotlight', error);
      throw new Error(ERROR_MESSAGES.DELETE_SPOTLIGHT);
    }
  }

  /**
   * Search store members for spotlight selection
   */
  static async searchStoreMembers(storeId: string, searchTerm: string = ''): Promise<StoreUser[]> {
    if (!validateStoreId(storeId)) {
      throw new Error('Invalid store ID');
    }

    let query = supabase
      .from(TABLE_NAMES.CLUB_MEMBERS)
      .select(QUERY_SELECTORS.STORE_MEMBER_SEARCH)
      .eq('book_clubs.store_id', storeId);

    if (searchTerm.trim()) {
      query = query.or(buildMemberSearchCondition(searchTerm));
    }

    const { data, error } = await query
      .order('joined_at', { ascending: false })
      .limit(QUERY_LIMITS.MEMBER_SEARCH);

    if (error) {
      logErrorWithContext('searchStoreMembers', error);
      throw new Error(ERROR_MESSAGES.SEARCH_MEMBERS);
    }

    return deduplicateStoreUsers(data || []);
  }
}
