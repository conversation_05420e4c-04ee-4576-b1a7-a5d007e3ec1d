import React, { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { CommunityShowcaseAPI, Testimonial, TestimonialFormData } from '@/lib/api/store/communityShowcase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Plus, 
  Star, 
  MessageCircle, 
  Edit, 
  Trash2, 
  Eye,
  EyeOff,
  ThumbsUp,
  ThumbsDown,
  GripVertical
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { toast } from 'sonner';

interface TestimonialManagerProps {
  storeId: string;
  testimonials: Testimonial[];
  onRefresh: () => void;
}

const SOURCE_TYPES = [
  { value: 'manual', label: 'Manual Entry' },
  { value: 'review_import', label: 'Imported Review' },
  { value: 'survey', label: 'Survey Response' },
  { value: 'social_media', label: 'Social Media' },
];

const APPROVAL_STATUS_COLORS = {
  pending: 'bg-yellow-100 text-yellow-800',
  approved: 'bg-green-100 text-green-800',
  rejected: 'bg-red-100 text-red-800',
};

export const TestimonialManager: React.FC<TestimonialManagerProps> = ({
  storeId,
  testimonials,
  onRefresh
}) => {
  const queryClient = useQueryClient();
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [editingTestimonial, setEditingTestimonial] = useState<Testimonial | null>(null);
  const [deleteTestimonialId, setDeleteTestimonialId] = useState<string | null>(null);
  
  const [formData, setFormData] = useState<TestimonialFormData>({
    customer_name: '',
    testimonial_text: '',
    rating: undefined,
    source_type: 'manual',
    source_url: '',
    is_anonymous: false,
    is_featured: false,
  });

  // Create testimonial mutation
  const createMutation = useMutation({
    mutationFn: (data: TestimonialFormData) => CommunityShowcaseAPI.createTestimonial(storeId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['community-showcase-admin', storeId] });
      queryClient.invalidateQueries({ queryKey: ['testimonials-admin', storeId] });
      setShowCreateDialog(false);
      resetForm();
      onRefresh();
      toast.success('Testimonial created successfully');
    },
    onError: (error) => {
      console.error('Error creating testimonial:', error);
      toast.error('Failed to create testimonial');
    },
  });

  // Update testimonial mutation
  const updateMutation = useMutation({
    mutationFn: ({ testimonialId, data }: { testimonialId: string; data: Partial<TestimonialFormData> }) =>
      CommunityShowcaseAPI.updateTestimonial(storeId, testimonialId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['community-showcase-admin', storeId] });
      queryClient.invalidateQueries({ queryKey: ['testimonials-admin', storeId] });
      setEditingTestimonial(null);
      resetForm();
      onRefresh();
      toast.success('Testimonial updated successfully');
    },
    onError: (error) => {
      console.error('Error updating testimonial:', error);
      toast.error('Failed to update testimonial');
    },
  });

  // Delete testimonial mutation
  const deleteMutation = useMutation({
    mutationFn: (testimonialId: string) => CommunityShowcaseAPI.deleteTestimonial(storeId, testimonialId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['community-showcase-admin', storeId] });
      queryClient.invalidateQueries({ queryKey: ['testimonials-admin', storeId] });
      setDeleteTestimonialId(null);
      onRefresh();
      toast.success('Testimonial deleted successfully');
    },
    onError: (error) => {
      console.error('Error deleting testimonial:', error);
      toast.error('Failed to delete testimonial');
    },
  });

  // Approval mutation
  const approvalMutation = useMutation({
    mutationFn: ({ testimonialId, status }: { testimonialId: string; status: 'approved' | 'rejected' }) =>
      CommunityShowcaseAPI.updateTestimonialApproval(storeId, testimonialId, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['community-showcase-admin', storeId] });
      queryClient.invalidateQueries({ queryKey: ['testimonials-admin', storeId] });
      onRefresh();
      toast.success('Testimonial approval updated');
    },
    onError: (error) => {
      console.error('Error updating approval:', error);
      toast.error('Failed to update approval status');
    },
  });

  const resetForm = () => {
    setFormData({
      customer_name: '',
      testimonial_text: '',
      rating: undefined,
      source_type: 'manual',
      source_url: '',
      is_anonymous: false,
      is_featured: false,
    });
  };

  const handleCreateTestimonial = () => {
    setEditingTestimonial(null);
    resetForm();
    setShowCreateDialog(true);
  };

  const handleEditTestimonial = (testimonial: Testimonial) => {
    setEditingTestimonial(testimonial);
    setFormData({
      customer_name: testimonial.customer_name || '',
      testimonial_text: testimonial.testimonial_text,
      rating: testimonial.rating || undefined,
      source_type: testimonial.source_type,
      source_url: testimonial.source_url || '',
      is_anonymous: testimonial.is_anonymous,
      is_featured: testimonial.is_featured,
    });
    setShowCreateDialog(true);
  };

  const handleSubmit = () => {
    if (!formData.testimonial_text.trim()) {
      toast.error('Please enter testimonial text');
      return;
    }

    if (!formData.is_anonymous && !formData.customer_name?.trim()) {
      toast.error('Please enter customer name or mark as anonymous');
      return;
    }

    const submitData = {
      ...formData,
      testimonial_text: formData.testimonial_text.trim(),
      customer_name: formData.is_anonymous ? undefined : formData.customer_name?.trim(),
      source_url: formData.source_url?.trim() || undefined,
    };

    if (editingTestimonial) {
      updateMutation.mutate({ testimonialId: editingTestimonial.id, data: submitData });
    } else {
      createMutation.mutate(submitData);
    }
  };

  const handleDeleteConfirm = () => {
    if (deleteTestimonialId) {
      deleteMutation.mutate(deleteTestimonialId);
    }
  };

  const renderStars = (rating?: number) => {
    if (!rating) return null;
    
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-4 w-4 ${
              star <= rating 
                ? 'text-yellow-400 fill-current' 
                : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  const approvedTestimonials = testimonials.filter(t => t.approval_status === 'approved');
  const pendingTestimonials = testimonials.filter(t => t.approval_status === 'pending');

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex justify-between items-center">
        <div className="text-sm text-gray-600">
          {approvedTestimonials.length} approved • {pendingTestimonials.length} pending
        </div>
        <Button onClick={handleCreateTestimonial}>
          <Plus className="h-4 w-4 mr-2" />
          Add Testimonial
        </Button>
      </div>

      {/* Testimonials List */}
      <div className="space-y-4">
        {testimonials.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <MessageCircle className="h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No testimonials yet</h3>
              <p className="text-gray-500 text-center mb-4">
                Start collecting customer testimonials to build social proof and trust.
              </p>
              <Button onClick={handleCreateTestimonial}>
                <Plus className="h-4 w-4 mr-2" />
                Add First Testimonial
              </Button>
            </CardContent>
          </Card>
        ) : (
          testimonials.map((testimonial) => (
            <Card key={testimonial.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0 space-y-3">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Badge className={APPROVAL_STATUS_COLORS[testimonial.approval_status]}>
                          {testimonial.approval_status}
                        </Badge>
                        {testimonial.is_featured && (
                          <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                            Featured
                          </Badge>
                        )}
                        <Badge variant="outline">
                          {SOURCE_TYPES.find(s => s.value === testimonial.source_type)?.label}
                        </Badge>
                      </div>
                      {testimonial.rating && renderStars(testimonial.rating)}
                    </div>

                    {/* Testimonial Text */}
                    <blockquote className="text-gray-900 italic leading-relaxed">
                      "{testimonial.testimonial_text}"
                    </blockquote>

                    {/* Attribution */}
                    <div className="text-sm text-gray-600">
                      — {testimonial.is_anonymous ? 'Anonymous Customer' : testimonial.customer_name}
                    </div>

                    {/* Metadata */}
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>Order: {testimonial.display_order}</span>
                      <span>Created: {new Date(testimonial.created_at).toLocaleDateString()}</span>
                      {testimonial.source_url && (
                        <a 
                          href={testimonial.source_url} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:underline"
                        >
                          View Source
                        </a>
                      )}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-2 ml-4">
                    {testimonial.approval_status === 'pending' && (
                      <>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => approvalMutation.mutate({ testimonialId: testimonial.id, status: 'approved' })}
                          className="text-green-600 hover:text-green-700"
                        >
                          <ThumbsUp className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => approvalMutation.mutate({ testimonialId: testimonial.id, status: 'rejected' })}
                          className="text-red-600 hover:text-red-700"
                        >
                          <ThumbsDown className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditTestimonial(testimonial)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setDeleteTestimonialId(testimonial.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Create/Edit Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {editingTestimonial ? 'Edit Testimonial' : 'Add New Testimonial'}
            </DialogTitle>
            <DialogDescription>
              Create a customer testimonial to build social proof and trust.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Anonymous Toggle */}
            <div className="flex items-center space-x-2">
              <Switch
                id="is_anonymous"
                checked={formData.is_anonymous}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_anonymous: checked }))}
              />
              <Label htmlFor="is_anonymous">Anonymous testimonial</Label>
            </div>

            {/* Customer Name */}
            {!formData.is_anonymous && (
              <div className="space-y-2">
                <Label>Customer Name *</Label>
                <Input
                  value={formData.customer_name}
                  onChange={(e) => setFormData(prev => ({ ...prev, customer_name: e.target.value }))}
                  placeholder="Enter customer name"
                  maxLength={100}
                />
              </div>
            )}

            {/* Testimonial Text */}
            <div className="space-y-2">
              <Label>Testimonial Text *</Label>
              <Textarea
                value={formData.testimonial_text}
                onChange={(e) => setFormData(prev => ({ ...prev, testimonial_text: e.target.value }))}
                placeholder="Enter the customer testimonial..."
                className="min-h-[120px]"
                maxLength={500}
              />
              <div className="text-sm text-gray-500 text-right">
                {formData.testimonial_text.length}/500 characters
              </div>
            </div>

            {/* Rating */}
            <div className="space-y-2">
              <Label>Rating (Optional)</Label>
              <Select
                value={formData.rating?.toString() || ''}
                onValueChange={(value) => setFormData(prev => ({ ...prev, rating: value ? parseInt(value) : undefined }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select rating" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">No rating</SelectItem>
                  {[1, 2, 3, 4, 5].map(rating => (
                    <SelectItem key={rating} value={rating.toString()}>
                      <div className="flex items-center space-x-2">
                        <span>{rating}</span>
                        <div className="flex">
                          {[1, 2, 3, 4, 5].map(star => (
                            <Star
                              key={star}
                              className={`h-3 w-3 ${star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
                            />
                          ))}
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Source Type */}
            <div className="space-y-2">
              <Label>Source Type</Label>
              <Select
                value={formData.source_type}
                onValueChange={(value: any) => setFormData(prev => ({ ...prev, source_type: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {SOURCE_TYPES.map(type => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Source URL */}
            <div className="space-y-2">
              <Label>Source URL (Optional)</Label>
              <Input
                value={formData.source_url}
                onChange={(e) => setFormData(prev => ({ ...prev, source_url: e.target.value }))}
                placeholder="https://..."
                type="url"
              />
            </div>

            {/* Featured Toggle */}
            <div className="flex items-center space-x-2">
              <Switch
                id="is_featured"
                checked={formData.is_featured}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_featured: checked }))}
              />
              <Label htmlFor="is_featured">Featured testimonial</Label>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleSubmit}
              disabled={createMutation.isPending || updateMutation.isPending || !formData.testimonial_text.trim()}
            >
              {createMutation.isPending || updateMutation.isPending ? 'Saving...' : 
               editingTestimonial ? 'Update Testimonial' : 'Create Testimonial'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deleteTestimonialId} onOpenChange={() => setDeleteTestimonialId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Testimonial</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this testimonial? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteConfirm}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
