/**
 * Community Metrics Config Component
 * Refactored main component using focused sub-components and custom hooks
 */

import React from 'react';
import type { CommunityMetricsConfigProps } from './types/metricsConfigTypes';
import { DisplaySettings } from './components/DisplaySettings';
import { MetricsDisplay } from './components/MetricsDisplay';
import { ActivityPreview } from './components/ActivityPreview';
import { ShowcaseSummary } from './components/ShowcaseSummary';

const METRIC_CARDS = [
  {
    key: 'active_members',
    icon: Users,
    label: 'Active Members',
    description: 'Community members participating in book clubs',
    color: 'bg-blue-500'
  },
  {
    key: 'total_clubs',
    icon: BookOpen,
    label: 'Book Clubs',
    description: 'Active reading groups and discussion circles',
    color: 'bg-green-500'
  },
  {
    key: 'recent_discussions',
    icon: MessageCircle,
    label: 'Recent Discussions',
    description: 'New topics started in the last 30 days',
    color: 'bg-purple-500'
  },
  {
    key: 'books_discussed_this_month',
    icon: TrendingUp,
    label: 'Books This Month',
    description: 'Books currently being read and discussed',
    color: 'bg-orange-500'
  },
  {
    key: 'new_members_this_month',
    icon: UserPlus,
    label: 'New Members',
    description: 'Fresh faces who joined us this month',
    color: 'bg-pink-500'
  }
];

export const CommunityMetricsConfig: React.FC<CommunityMetricsConfigProps> = ({
  storeId,
  metrics,
  activities,
  settings,
  onSettingsUpdate,
  onRefresh
}) => {
  const formatValue = (num: number) => {
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k';
    }
    return num.toString();
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return 'Just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes}m ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours}h ago`;
    } else {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days}d ago`;
    }
  };

  return (
    <div className="space-y-6">
      {/* Settings Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Display Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Activity Feed Settings */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-base font-medium">Activity Feed</Label>
                <p className="text-sm text-gray-600">Show recent community activity on landing page</p>
              </div>
              <Switch
                checked={settings.show_activity_feed}
                onCheckedChange={(checked) => onSettingsUpdate({ show_activity_feed: checked })}
              />
            </div>

            {settings.show_activity_feed && (
              <div className="ml-4 space-y-2">
                <Label>Number of activities to display</Label>
                <Input
                  type="number"
                  min="3"
                  max="10"
                  value={settings.activity_feed_limit}
                  onChange={(e) => onSettingsUpdate({ activity_feed_limit: parseInt(e.target.value) || 5 })}
                  className="w-24"
                />
              </div>
            )}
          </div>

          {/* Community Metrics Settings */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-base font-medium">Community Metrics</Label>
                <p className="text-sm text-gray-600">Display community statistics and growth</p>
              </div>
              <Switch
                checked={settings.show_community_metrics}
                onCheckedChange={(checked) => onSettingsUpdate({ show_community_metrics: checked })}
              />
            </div>
          </div>

          {/* Member Spotlights Settings */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-base font-medium">Member Spotlights</Label>
                <p className="text-sm text-gray-600">Feature active community members</p>
              </div>
              <Switch
                checked={settings.show_member_spotlights}
                onCheckedChange={(checked) => onSettingsUpdate({ show_member_spotlights: checked })}
              />
            </div>

            {settings.show_member_spotlights && (
              <div className="ml-4 space-y-2">
                <Label>Maximum spotlights to display</Label>
                <Input
                  type="number"
                  min="1"
                  max="6"
                  value={settings.max_spotlights_display}
                  onChange={(e) => onSettingsUpdate({ max_spotlights_display: parseInt(e.target.value) || 3 })}
                  className="w-24"
                />
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Current Metrics Display */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Current Community Metrics
            </CardTitle>
            <Button variant="outline" size="sm" onClick={onRefresh}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {metrics ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {METRIC_CARDS.map((card) => {
                const IconComponent = card.icon;
                const value = metrics[card.key as keyof CommunityMetrics] || 0;

                return (
                  <div key={card.key} className="bg-gradient-to-br from-white to-gray-50/50 rounded-lg p-4 border border-gray-100">
                    <div className="flex items-center justify-between mb-3">
                      <div className={`p-2 rounded-lg ${card.color}`}>
                        <IconComponent className="h-5 w-5 text-white" />
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-bookconnect-brown">
                          {formatValue(value)}
                        </div>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium text-bookconnect-brown text-sm mb-1">
                        {card.label}
                      </h4>
                      <p className="text-xs text-bookconnect-brown/60 leading-relaxed">
                        {card.description}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-30" />
              <p>Loading community metrics...</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Activity Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Recent Activity Preview
          </CardTitle>
        </CardHeader>
        <CardContent>
          {activities.length > 0 ? (
            <div className="space-y-3">
              {activities.slice(0, settings.activity_feed_limit).map((activity, index) => (
                <div key={activity.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                  <div className="flex-shrink-0 p-2 bg-blue-100 rounded-lg">
                    {activity.type === 'discussion' && <MessageCircle className="h-4 w-4 text-blue-600" />}
                    {activity.type === 'member_join' && <UserPlus className="h-4 w-4 text-green-600" />}
                    {activity.type === 'book_set' && <BookOpen className="h-4 w-4 text-purple-600" />}
                    {activity.type === 'club_created' && <Users className="h-4 w-4 text-orange-600" />}
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-gray-900 text-sm truncate">
                          {activity.title}
                        </h4>
                        <p className="text-sm text-gray-600 mt-1">
                          {activity.description}
                        </p>

                        <div className="flex items-center space-x-2 mt-2">
                          {activity.user_name && (
                            <Badge variant="outline" className="text-xs">
                              {activity.user_name}
                            </Badge>
                          )}
                          {activity.club_name && (
                            <Badge variant="secondary" className="text-xs">
                              {activity.club_name}
                            </Badge>
                          )}
                        </div>
                      </div>

                      <div className="flex-shrink-0 ml-2">
                        <div className="flex items-center text-xs text-gray-500">
                          <Clock className="h-3 w-3 mr-1" />
                          <span>{formatTimeAgo(activity.created_at)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              {activities.length > settings.activity_feed_limit && (
                <div className="text-center pt-2 text-sm text-gray-500">
                  Showing {settings.activity_feed_limit} of {activities.length} activities
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Activity className="h-12 w-12 mx-auto mb-4 opacity-30" />
              <p>No recent activity</p>
              <p className="text-sm mt-1">Activity will appear as members interact with your community</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Community Showcase Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-bookconnect-brown">
                {settings.show_member_spotlights ? '✓' : '✗'}
              </div>
              <div className="text-sm text-gray-600">Member Spotlights</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-bookconnect-brown">
                {settings.show_testimonials ? '✓' : '✗'}
              </div>
              <div className="text-sm text-gray-600">Testimonials</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-bookconnect-brown">
                {settings.show_activity_feed ? '✓' : '✗'}
              </div>
              <div className="text-sm text-gray-600">Activity Feed</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-bookconnect-brown">
                {settings.show_community_metrics ? '✓' : '✗'}
              </div>
              <div className="text-sm text-gray-600">Community Metrics</div>
            </div>
          </div>

          <div className="mt-4 p-4 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>Tip:</strong> Enable at least one feature to display the Community Showcase section on your landing page.
              The section will automatically hide if no features are enabled or if there's no content to display.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
