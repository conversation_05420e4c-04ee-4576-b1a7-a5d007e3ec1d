/**
 * Metrics and Activity API Module
 * Handles community metrics and activity feed operations
 */

import { supabase } from '@/lib/supabase';
import type { 
  CommunityMetrics, 
  ActivityFeedItem 
} from '../types/communityShowcaseTypes';
import { 
  TABLE_NAMES, 
  QUERY_SELECTORS, 
  QUERY_LIMITS 
} from '../constants/communityShowcaseConstants';
import { 
  getThirtyDaysAgo,
  getFirstOfMonth,
  createEmptyMetrics,
  processDiscussionsToActivities,
  processMemberJoinsToActivities,
  sortActivitiesByDate,
  validateStoreId,
  logErrorWithContext
} from '../utils/communityShowcaseUtils';

/**
 * Metrics and Activity API operations
 */
export class MetricsActivityAPI {
  /**
   * Calculate community metrics from existing data
   */
  static async getCommunityMetrics(storeId: string): Promise<CommunityMetrics> {
    if (!validateStoreId(storeId)) {
      throw new Error('Invalid store ID');
    }

    try {
      // Get active members count
      const { count: activeMembersCount } = await supabase
        .from(TABLE_NAMES.CLUB_MEMBERS)
        .select('user_id', { count: 'exact', head: true })
        .in('club_id',
          supabase
            .from(TABLE_NAMES.BOOK_CLUBS)
            .select('id')
            .eq('store_id', storeId)
        );

      // Get total clubs count
      const { count: totalClubsCount } = await supabase
        .from(TABLE_NAMES.BOOK_CLUBS)
        .select('id', { count: 'exact', head: true })
        .eq('store_id', storeId);

      // Get recent discussions count (last 30 days)
      const thirtyDaysAgo = getThirtyDaysAgo();

      const { count: recentDiscussionsCount } = await supabase
        .from(TABLE_NAMES.DISCUSSION_TOPICS)
        .select('id', { count: 'exact', head: true })
        .in('club_id',
          supabase
            .from(TABLE_NAMES.BOOK_CLUBS)
            .select('id')
            .eq('store_id', storeId)
        )
        .gte('created_at', thirtyDaysAgo.toISOString());

      // Get books discussed this month (from current_books)
      const firstOfMonth = getFirstOfMonth();

      const { count: booksDiscussedCount } = await supabase
        .from(TABLE_NAMES.CURRENT_BOOKS)
        .select('club_id', { count: 'exact', head: true })
        .in('club_id',
          supabase
            .from(TABLE_NAMES.BOOK_CLUBS)
            .select('id')
            .eq('store_id', storeId)
        )
        .gte('set_at', firstOfMonth.toISOString());

      // Get new members this month
      const { count: newMembersCount } = await supabase
        .from(TABLE_NAMES.CLUB_MEMBERS)
        .select('user_id', { count: 'exact', head: true })
        .in('club_id',
          supabase
            .from(TABLE_NAMES.BOOK_CLUBS)
            .select('id')
            .eq('store_id', storeId)
        )
        .gte('joined_at', firstOfMonth.toISOString());

      return {
        active_members: activeMembersCount || 0,
        total_clubs: totalClubsCount || 0,
        recent_discussions: recentDiscussionsCount || 0,
        books_discussed_this_month: booksDiscussedCount || 0,
        new_members_this_month: newMembersCount || 0,
      };
    } catch (error) {
      logErrorWithContext('getCommunityMetrics', error);
      return createEmptyMetrics();
    }
  }

  /**
   * Get recent community activity feed
   */
  static async getRecentActivity(storeId: string, limit: number = QUERY_LIMITS.DEFAULT_ACTIVITY_FEED): Promise<ActivityFeedItem[]> {
    if (!validateStoreId(storeId)) {
      throw new Error('Invalid store ID');
    }

    try {
      const activities: ActivityFeedItem[] = [];

      // Get recent discussion topics
      const { data: discussions } = await supabase
        .from(TABLE_NAMES.DISCUSSION_TOPICS)
        .select(QUERY_SELECTORS.DISCUSSION_WITH_CLUB_AND_USER)
        .eq('book_clubs.store_id', storeId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (discussions) {
        activities.push(...processDiscussionsToActivities(discussions));
      }

      // Get recent member joins
      const { data: newMembers } = await supabase
        .from(TABLE_NAMES.CLUB_MEMBERS)
        .select(QUERY_SELECTORS.MEMBER_WITH_CLUB_AND_USER)
        .eq('book_clubs.store_id', storeId)
        .order('joined_at', { ascending: false })
        .limit(limit);

      if (newMembers) {
        activities.push(...processMemberJoinsToActivities(newMembers));
      }

      // Sort all activities by date and limit
      return sortActivitiesByDate(activities).slice(0, limit);

    } catch (error) {
      logErrorWithContext('getRecentActivity', error);
      return [];
    }
  }
}
